# Configuration Guide

This guide explains how to configure the Carbon Regulation News application using the new YAML-based configuration system.

## Overview

The application now supports human-editable YAML configuration, making it easy to customize news collection behavior, search queries, sources, and all other settings without modifying code.

## Configuration Files

### Primary Configuration: `config.yaml`

The main configuration file contains all application settings organized into logical sections:

- **Environment Settings**: Debug mode, logging level, environment type
- **API Keys**: OpenRouter and Tavily API credentials
- **Database**: Connection settings and options
- **API Server**: Host, port, and debug settings
- **News Collector**: Search queries, sources, and collection behavior
- **Scheduler**: Task timing and retry settings
- **Notifications**: Webhook and email notification settings
- **AI Processing**: Model settings and analysis options
- **Content Filtering**: Keywords and quality filters
- **Monitoring**: Health checks and alerting thresholds

### Legacy Support: `.env` file

Environment variables are still supported for backward compatibility and can be used alongside or instead of YAML configuration.

## Key Configuration Sections

### News Collector Configuration

The most important section for customizing news collection:

```yaml
news_collector:
  # Basic settings
  max_articles_per_source: 10
  default_time_range: "day"  # hour, day, week, month
  
  # Search queries - customize these for your specific needs
  search_queries:
    - "carbon regulations emission standards environmental policy"
    - "clean energy regulations carbon policy sustainability"
    - "carbon accounting standards disclosure reporting"
    - "carbon pricing markets ETS carbon tax"
    - "climate regulations environmental compliance"
    - "carbon border adjustment mechanism CBAM"
    - "scope 3 emissions reporting requirements"
    - "net zero commitments corporate sustainability"
    - "carbon offset verification standards"
    - "renewable energy mandates policy"
  
  # Specific sources to monitor
  specific_sources:
    - "https://www.reuters.com/sustainability/clean-energy/"
    - "https://www.reuters.com/sustainability/climate-energy/"
    - "https://www.bloomberg.com/green"
    - "https://www.carbonbrief.org/"
    - "https://www.climatechangenews.com/"
  
  # Advanced search settings
  search_settings:
    include_domains: []  # Empty means all domains
    exclude_domains:
      - "twitter.com"
      - "facebook.com"
      - "instagram.com"
      - "tiktok.com"
    search_depth: "basic"  # or "advanced"
    extract_full_content: true
    min_content_length: 100
```

### Content Filtering

Control what content gets collected and prioritized:

```yaml
content_filtering:
  # Keywords that must be present (OR logic)
  required_keywords:
    - "carbon"
    - "emission"
    - "climate"
    - "sustainability"
    - "environmental"
    - "green"
    - "renewable"
  
  # Keywords that indicate high priority
  priority_keywords:
    - "regulation"
    - "policy"
    - "law"
    - "mandate"
    - "requirement"
    - "compliance"
    - "standard"
  
  # Keywords to exclude content
  exclude_keywords:
    - "sports"
    - "entertainment"
    - "celebrity"
  
  # Language filtering
  languages:
    - "en"  # English
    - "es"  # Spanish (if needed)
  
  # Content quality filters
  quality_filters:
    min_word_count: 50
    max_word_count: 10000
    require_publication_date: true
```

## Configuration Management

### Testing Configuration

Test your configuration changes:

```bash
# Validate and test the configuration
python scripts/test_config.py
```

### Migration from Environment Variables

If you're currently using environment variables:

```bash
# Migrate existing .env settings to YAML
python scripts/migrate_config.py
```

### Reloading Configuration

The application automatically loads configuration on startup. To reload configuration in a running application, restart the service or use the reload function in code:

```python
from app.core.config import reload_settings
settings = reload_settings()
```

## Common Customizations

### Adding New Search Queries

1. Edit `config.yaml`
2. Add new queries to the `news_collector.search_queries` list
3. Test with `python scripts/test_config.py`
4. Restart the application

### Adding New News Sources

1. Edit `config.yaml`
2. Add URLs to the `news_collector.specific_sources` list
3. Test the configuration
4. Monitor collection results

### Customizing Content Filters

1. Modify `content_filtering.required_keywords` for your industry
2. Add industry-specific terms to `priority_keywords`
3. Update `exclude_keywords` to filter out unwanted content
4. Adjust `quality_filters` for content length requirements

### Scheduling Changes

1. Modify `scheduler.daily_run_time` for different collection times
2. Adjust `scheduler.task_timeout_minutes` for longer/shorter timeouts
3. Configure `scheduler.retry_settings` for error handling

## API Keys Configuration

API keys can be set in multiple ways (in order of precedence):

1. Environment variables: `OPENROUTER_API_KEY`, `TAVILY_API_KEY`
2. YAML configuration: `api_keys.openrouter_api_key`, `api_keys.tavily_api_key`
3. `.env` file variables

Example YAML configuration:

```yaml
api_keys:
  openrouter_api_key: "your-openrouter-key-here"
  tavily_api_key: "your-tavily-key-here"
```

## Best Practices

1. **Version Control**: Keep `config.yaml` in version control but exclude sensitive API keys
2. **Environment-Specific**: Use different config files for development/production
3. **Testing**: Always test configuration changes with `scripts/test_config.py`
4. **Backup**: Keep backups of working configurations before making changes
5. **Documentation**: Document custom search queries and their purposes

## Troubleshooting

### Configuration Not Loading

1. Check YAML syntax with `python -c "import yaml; yaml.safe_load(open('config.yaml'))"`
2. Verify file permissions and location
3. Check application logs for configuration errors

### Search Queries Not Working

1. Test individual queries manually
2. Check for typos in keywords
3. Verify API keys are correctly configured
4. Review content filtering settings

### No Articles Collected

1. Check if search queries are too restrictive
2. Verify news sources are accessible
3. Review content filtering criteria
4. Check API rate limits and quotas

## Support

For additional help:

1. Run the test script: `python scripts/test_config.py`
2. Check application logs for detailed error messages
3. Review the README.md for setup instructions
4. Validate YAML syntax online or with tools
