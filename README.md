# Carbon Regulation News Application

A production-ready application for collecting, analyzing, and summarizing carbon regulation and climate policy news using AI-powered analysis and automated scheduling.

## Features

- **Automated News Collection**: Collect news from multiple sources using web search and specific URLs
- **AI-Powered Analysis**: Process articles with advanced AI to extract structured information
- **Daily Summaries**: Generate comprehensive daily summaries for climate experts
- **Task Management**: Monitor and manually trigger collection and processing tasks
- **REST API**: Full-featured API for accessing news, summaries, and task management
- **Notification System**: Extensible notification system with webhook and Slack support
- **Comprehensive Testing**: Full test suite with unit and integration tests

## Architecture

The application follows a clean, layered architecture:

```
app/
├── core/           # Core components (config, database, models, logging)
├── services/       # Business logic (news collection, AI parsing, scheduling, notifications)
├── api/           # REST API endpoints and schemas
└── scripts/       # Standalone scripts for manual execution

tests/
├── test_services/ # Unit tests for services
├── test_api/      # API endpoint tests
└── test_integration/ # End-to-end integration tests
```

## Quick Start

### Prerequisites

- Python 3.12+
- API keys for:
  - [Tavi<PERSON>](https://tavily.com) (for web search and content extraction)
  - [OpenRouter](https://openrouter.ai) (for AI processing)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd carbon-regulation-news
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Initialize the database**
   ```bash
   python -c "from app.core.database import DatabaseManager; DatabaseManager.initialize()"
   ```

6. **Start the API server**
   ```bash
   python -m app.api.main
   ```

The API will be available at `http://localhost:8000` with interactive documentation at `http://localhost:8000/docs`.

## Configuration

The application supports two configuration methods:

### 1. YAML Configuration (Recommended)

The application now uses a `config.yaml` file for easy human-editable configuration. This file contains all settings for the news collector and other components.

**Quick Start:**
```bash
# The config.yaml file is already included in the repository
# Edit it directly to customize your settings
nano config.yaml

# Test your configuration
python scripts/test_config.py
```

**Key sections in config.yaml:**
- `news_collector`: Search queries, sources, and collection settings
- `api_keys`: API credentials (can also use environment variables)
- `database`: Database connection settings
- `scheduler`: Task scheduling configuration
- `notifications`: Webhook and notification settings
- `ai_processing`: AI model and analysis settings
- `content_filtering`: Content quality and keyword filters

### 2. Environment Variables (Legacy)

You can still use environment variables for configuration. Create a `.env` file in the project root:

```env
# API Keys (Required)
TAVILY_API_KEY=your_tavily_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Database Configuration
DATABASE__URL=sqlite:///./carbon_news.db

# API Configuration
API__HOST=0.0.0.0
API__PORT=8000
API__DEBUG=false

# Logging
LOG_LEVEL=INFO
ENVIRONMENT=production

# Scheduler Configuration
SCHEDULER__DAILY_RUN_TIME=09:00
SCHEDULER__MAX_TASK_HISTORY=100

# Notifications (Optional)
NOTIFICATIONS__WEBHOOK_URL=https://your-webhook-url.com
NOTIFICATIONS__SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
NOTIFICATIONS__ENABLE_NOTIFICATIONS=true

# News Collection
NEWS_COLLECTOR__MAX_ARTICLES_PER_SOURCE=10
NEWS_COLLECTOR__DEFAULT_TIME_RANGE=day
```

### Configuration Migration

To migrate from environment variables to YAML:

```bash
# Migrate existing environment variables to YAML
python scripts/migrate_config.py

# This will merge your .env settings with config.yaml
```

### Customizing News Collection

The YAML configuration makes it easy to customize news collection behavior:

**Search Queries:** Edit the `news_collector.search_queries` section to target specific topics:
```yaml
news_collector:
  search_queries:
    - "carbon regulations emission standards environmental policy"
    - "ESG reporting requirements sustainability"
    - "your custom search query here"
```

**News Sources:** Add specific websites or RSS feeds to monitor:
```yaml
news_collector:
  specific_sources:
    - "https://www.reuters.com/sustainability/clean-energy/"
    - "https://your-industry-news-site.com/rss"
```

**Content Filtering:** Customize what content gets collected:
```yaml
content_filtering:
  required_keywords:
    - "carbon"
    - "sustainability"
    - "your-industry-term"
  priority_keywords:
    - "regulation"
    - "compliance"
  exclude_keywords:
    - "sports"
    - "entertainment"
```

**Advanced Search Settings:** Fine-tune search behavior:
```yaml
news_collector:
  search_settings:
    search_depth: "advanced"  # or "basic"
    exclude_domains:
      - "twitter.com"
      - "facebook.com"
    min_content_length: 200
```

## Usage

### Running Daily Tasks

**Automated (Recommended):**
The application includes a built-in scheduler that runs daily tasks automatically.

**Manual Execution:**
```bash
# Run the complete pipeline (collection + processing + summary)
python -m app.scripts.run_daily_task --task-type full-pipeline

# Run individual tasks
python -m app.scripts.run_daily_task --task-type collection
python -m app.scripts.run_daily_task --task-type processing
python -m app.scripts.run_daily_task --task-type summary

# Dry run to see what would be executed
python -m app.scripts.run_daily_task --task-type full-pipeline --dry-run
```

### API Usage

**Health Check:**
```bash
curl http://localhost:8000/health/
```

**Get Latest News:**
```bash
curl http://localhost:8000/news/articles?page=1&page_size=10
```

**Get Daily Summary:**
```bash
curl http://localhost:8000/news/summaries/latest
```

**Trigger Manual Task:**
```bash
curl -X POST http://localhost:8000/tasks/trigger \
  -H "Content-Type: application/json" \
  -d '{"task_type": "full_pipeline"}'
```

**Monitor Task Executions:**
```bash
curl http://localhost:8000/tasks/executions?page=1&page_size=20
```

### API Documentation

Interactive API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
- OpenAPI JSON: `http://localhost:8000/openapi.json`

## Testing

Run the complete test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/test_services/          # Unit tests
pytest tests/test_api/              # API tests
pytest tests/test_integration/      # Integration tests

# Run with verbose output
pytest -v

# Run specific test file
pytest tests/test_services/test_news_collector.py -v
```

## Monitoring and Logging

### Logging

The application uses structured logging with configurable levels:

```bash
# Set log level via environment variable
export LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR

# Logs are written to both console and app.log file
tail -f app.log
```

### Health Monitoring

Monitor application health:

```bash
# Basic health check
curl http://localhost:8000/health/

# Detailed system statistics
curl http://localhost:8000/health/statistics

# Database health
curl http://localhost:8000/health/database

# Scheduler status
curl http://localhost:8000/health/scheduler
```

### Task Monitoring

Monitor task executions and results:

```bash
# Recent task activity
curl http://localhost:8000/tasks/recent

# Task execution history
curl http://localhost:8000/tasks/executions

# Task results and summaries
curl http://localhost:8000/tasks/results
```

## Notifications

The application supports multiple notification channels:

### Webhook Notifications

Set `NOTIFICATIONS__WEBHOOK_URL` in your `.env` file. The application will send POST requests with task completion information.

### Slack Notifications

1. Create a Slack webhook URL in your workspace
2. Set `NOTIFICATIONS__SLACK_WEBHOOK_URL` in your `.env` file
3. Notifications will be sent with rich formatting

### Adding Custom Notification Channels

Extend the notification system by implementing the `BaseNotificationChannel` interface:

```python
from app.services.notifications import BaseNotificationChannel, NotificationType

class CustomNotificationChannel(BaseNotificationChannel):
    def get_channel_type(self) -> NotificationType:
        return NotificationType.EMAIL  # or custom type
    
    def validate_config(self) -> bool:
        return "api_key" in self.config
    
    def send(self, subject: str, message: str, payload: dict) -> dict:
        # Implement your notification logic
        pass
```

## Development

### Project Structure

```
carbon-regulation-news/
├── app/
│   ├── core/              # Core components
│   │   ├── config.py      # Configuration management
│   │   ├── database.py    # Database setup
│   │   ├── models.py      # SQLAlchemy models
│   │   └── logging.py     # Logging configuration
│   ├── services/          # Business logic
│   │   ├── news_collector.py    # News collection
│   │   ├── ai_parser.py         # AI processing
│   │   ├── scheduler.py         # Task scheduling
│   │   └── notifications.py     # Notification system
│   ├── api/               # REST API
│   │   ├── main.py        # FastAPI application
│   │   ├── schemas.py     # Pydantic models
│   │   └── routes/        # API endpoints
│   └── scripts/           # Standalone scripts
├── tests/                 # Test suite
├── requirements.txt       # Dependencies
├── .env.example          # Environment template
└── README.md             # This file
```

### Code Quality

The project follows Python best practices:

- **Type hints**: Full type annotation coverage
- **Logging**: Structured logging throughout
- **Error handling**: Comprehensive error handling and recovery
- **Testing**: High test coverage with unit and integration tests
- **Documentation**: Inline documentation and API docs

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run the test suite
5. Submit a pull request

## Troubleshooting

### Common Issues

**Database Connection Errors:**
```bash
# Reset the database
python -c "from app.core.database import DatabaseManager; DatabaseManager.reset()"
```

**API Key Issues:**
- Verify your API keys are correctly set in `.env`
- Check API key permissions and quotas
- Test API keys independently

**Import Errors:**
```bash
# Ensure you're in the project root and virtual environment is activated
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

**Port Already in Use:**
```bash
# Change the port in .env or kill the existing process
export API__PORT=8001
```

### Logs and Debugging

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
export API__DEBUG=true
```

Check application logs:
```bash
tail -f app.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the [API documentation](http://localhost:8000/docs)
- Review the test suite for usage examples
- Open an issue for bugs or feature requests
